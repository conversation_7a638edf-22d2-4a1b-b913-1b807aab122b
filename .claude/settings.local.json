{"permissions": {"allow": ["<PERSON><PERSON>(curl:*)", "Bash(npm run test:e2e:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm run build:*)", "Bash(npm install:*)", "Bash(npm test)", "Bash(npm run test:coverage:*)", "Bash(npx puppeteer browsers:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(pkill:*)", "Bash(npm run postinstall:*)", "Bash(grep:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(rm:*)", "Bash(apt list:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(git worktree:*)", "Bash(git checkout:*)", "Bash(npm run lint)", "<PERSON><PERSON>(clickup-sync:*)", "Bash(npx prisma migrate dev:*)", "Bash(npx prisma migrate reset:*)", "Bash(npm run dev:*)", "Bash(npx ts-node:*)", "Bash(npx tsx:*)", "Bash(node:*)", "Bash(npm run pre-release:*)", "Bash(npm run qa:teardown:*)", "Bash(docker system prune:*)", "<PERSON><PERSON>(docker stop:*)", "<PERSON><PERSON>(docker rm:*)", "Bash(docker logs:*)", "Bash(npm run:*)", "Bash(DATABASE_URL=\"postgresql://voxstudent:voxstudent@localhost:5433/voxstudent_qa\" npx prisma migrate deploy)", "Bash(DATABASE_URL=\"postgresql://voxstudent:voxstudent@localhost:5433/voxstudent_qa\" npx prisma migrate dev --name initial_postgresql)", "Bash(DATABASE_URL=\"postgresql://voxstudent:voxstudent@localhost:5433/voxstudent_qa\" npx prisma db seed)", "<PERSON><PERSON>(docker restart:*)", "<PERSON><PERSON>(docker exec:*)", "Bash(DATABASE_URL=\"postgresql://voxstudent:voxstudent@localhost:5433/voxstudent_qa\" npx prisma db push)", "Bash(psql:*)", "<PERSON><PERSON>(docker ps:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(DATABASE_URL=\"postgresql://voxstudent:voxstudent@localhost:5433/voxstudent_qa\" npx prisma migrate dev --name initial_postgresql_qa)", "Bash(timeout 10 docker exec voxstudent-app-qa wget -O- http://localhost:3001/api/health)", "Bash(./scripts/pre-release-qa.sh:*)", "Bash(./scripts/build.sh:*)", "Bash(git add:*)", "Bash(git commit:*)"], "deny": []}}