# Sistema de Reposição de Aulas

Este documento descreve o sistema automatizado de lembretes de reposição de aulas implementado no VoxStudent.

## Visão Geral

O sistema de reposição detecta automaticamente quando um aluno falta a uma aula e agenda um lembrete para ser enviado no dia seguinte às 9h da manhã via WhatsApp. O lembrete contém um link de autenticação (OTP) válido por 24h que permite ao aluno escolher uma aula de reposição.

## Fluxo do Sistema

1. **Detecção de Ausência**: Quando a presença é marcada e há faltas registradas
2. **Agendamento de Lembrete**: Sistema agenda lembrete para 9h do dia seguinte
3. **Processamento**: Cron job processa lembretes pendentes a cada hora
4. **Envio via WhatsApp**: Mensagem com link OTP é enviada ao aluno
5. **Autenticação**: Aluno clica no link e é autenticado via OTP
6. **Seleção de Reposição**: Aluno escolhe aula disponível para reposição
7. **Confirmação**: Sistema registra a reposição e cria presença automática

## Requisitos

### Para o Curso
- Deve ter `allowsMakeup: true` no banco de dados
- Deve ter aulas futuras disponíveis na mesma turma/curso

### Para o Aluno
- Deve ter número de telefone cadastrado
- Deve estar matriculado no curso
- Deve ter registrado ausência em aula elegível para reposição

### Para o Sistema
- WhatsApp deve estar configurado e conectado
- Cron jobs devem estar executando
- Base de dados deve estar acessível

## Modelos de Dados

### RecoveryReminder
```typescript
{
  id: string
  studentId: string
  attendanceId: string  // A ausência que gerou o lembrete
  reminderDate: DateTime // Quando enviar (9h do dia seguinte)
  sentAt?: DateTime     // Quando foi enviado
  status: 'pending' | 'sent' | 'failed' | 'cancelled'
  messageText?: string  // Texto da mensagem enviada
  errorMessage?: string // Erro se falhou
}
```

### RecoveryOtp
```typescript
{
  id: string
  studentId: string
  attendanceId: string  // A ausência para qual foi gerado
  token: string        // Token único para autenticação
  expiresAt: DateTime  // Válido por 24h
  usedAt?: DateTime    // Quando foi usado
}
```

### RecoveryBooking
```typescript
{
  id: string
  studentId: string
  originalLessonId: string  // Aula que foi perdida
  makeupLessonId: string    // Aula de reposição escolhida
  otpId: string            // OTP usado para fazer a reserva
  bookedAt: DateTime       // Quando foi agendada
  status: 'booked' | 'completed' | 'cancelled'
}
```

## API Endpoints

### GET /api/recovery/validate
Valida um token OTP e retorna dados da ausência.

**Query Parameters:**
- `token`: Token OTP para validação

**Response:**
```json
{
  "data": {
    "student": { "id": "...", "name": "...", "phone": "..." },
    "attendance": {
      "lesson": {
        "id": "...",
        "title": "...",
        "scheduledDate": "...",
        "class": { "course": { "name": "..." } }
      }
    }
  }
}
```

### GET /api/recovery/available-lessons
Lista aulas disponíveis para reposição.

**Query Parameters:**
- `courseId`: ID do curso
- `token`: Token OTP válido

**Response:**
```json
{
  "data": [
    {
      "id": "...",
      "title": "...",
      "scheduledDate": "...",
      "class": {
        "name": "...",
        "course": { "name": "..." }
      }
    }
  ]
}
```

### POST /api/recovery/book
Agenda uma aula de reposição.

**Body:**
```json
{
  "token": "otp-token",
  "originalLessonId": "lesson-id",
  "makeupLessonId": "makeup-lesson-id"
}
```

### POST /api/recovery/process (Admin)
Processa manualmente lembretes pendentes.

### GET /api/recovery/process (Admin)
Obtém estatísticas do sistema de reposição.

### POST /api/recovery/cleanup (Admin)
Remove dados antigos (lembretes > 30 dias, OTPs expirados).

## Serviços

### RecoveryService
Principais métodos:
- `createRecoveryReminder()`: Cria lembrete para ausência
- `generateRecoveryOtp()`: Gera token OTP para autenticação
- `validateRecoveryOtp()`: Valida token OTP
- `getAvailableMakeupLessons()`: Lista aulas disponíveis
- `bookMakeupLesson()`: Agenda reposição
- `scheduleRecoveryReminders()`: Agenda lembretes para aula

### RecoverySchedulerService
Principais métodos:
- `processPendingReminders()`: Processa lembretes pendentes
- `getRecoveryReminderStats()`: Estatísticas do sistema
- `cleanupOldData()`: Remove dados antigos

## Configuração de Cron Jobs

### Desenvolvimento
Use o script fornecido:
```bash
node scripts/setup-recovery-cron.js
```

### Produção
Configure como serviço systemd ou usando node-cron:

```javascript
// Processar lembretes a cada hora
cron.schedule('0 * * * *', processRecoveryReminders);

// Limpeza diária às 2h
cron.schedule('0 2 * * *', cleanupRecoveryData);
```

## Integração com Sistema Existente

### Attendance API
O sistema se integra automaticamente com a API de presença:
- `POST /api/attendance`: Agenda lembretes para novas ausências
- `PUT /api/attendance`: Agenda lembretes se status mudar para ausente

### WhatsApp Integration
Utiliza a fila de mensagens existente:
- Mensagens têm prioridade alta (priority: 2)
- Tipo: 'recovery_reminder'
- Processadas pelo sistema WhatsApp existente

## Página de Reposição

Localizada em `/recovery?token=<otp-token>`, permite:
- Visualizar dados da ausência
- Ver aulas disponíveis para reposição
- Selecionar e agendar reposição
- Confirmação automática

## Testes

Execute os testes E2E:
```bash
npm run test:e2e tests/e2e/recovery-system.spec.ts
```

Os testes cobrem:
- Criação de lembretes
- Geração e validação de OTP
- APIs de reposição
- Fluxo completo via UI
- Prevenção de duplicatas
- Tratamento de erros

## Administração

Use o componente `RecoveryManagement` no painel admin:
- Visualizar estatísticas
- Processar lembretes manualmente
- Limpar dados antigos
- Monitorar status do sistema

## Monitoramento

### Logs
O sistema registra:
- Criação de lembretes
- Processamento de lembretes
- Envio de mensagens
- Bookings de reposição
- Erros e falhas

### Métricas
- Total de lembretes criados
- Taxa de sucesso de envio
- Lembretes pendentes
- Reposições agendadas

## Troubleshooting

### Lembretes não sendo enviados
1. Verificar se WhatsApp está conectado
2. Verificar cron jobs executando
3. Verificar telefones dos alunos
4. Verificar se curso permite reposição

### Tokens OTP inválidos
1. Verificar se token não expirou (24h)
2. Verificar se não foi usado anteriormente
3. Verificar se aluno/ausência existem

### Aulas não disponíveis
1. Verificar se há aulas futuras no curso
2. Verificar se aulas não estão completas
3. Verificar se curso permite reposição

### Duplicatas
Sistema previne automaticamente:
- Um lembrete por ausência
- Um OTP ativo por ausência
- Uma reposição por ausência

## Configurações Recomendadas

### Produção
- Processar lembretes a cada hora
- Limpeza diária às 2h
- Logs em arquivo rotativo
- Monitoramento de fila WhatsApp
- Backup regular dos dados

### Desenvolvimento
- Processar lembretes manualmente via API
- Usar dados de teste
- Logs no console
- WhatsApp em modo de teste

## Segurança

- Tokens OTP são criptograficamente seguros (32 bytes)
- Expiração automática em 24h
- Validação de origem (aluno/ausência)
- Prevenção de replay attacks
- Logs de auditoria completos

## Performance

- Processamento em lote de lembretes
- Queries otimizadas com índices
- Limpeza automática de dados antigos
- Cache de resultados quando possível
- Processamento assíncrono