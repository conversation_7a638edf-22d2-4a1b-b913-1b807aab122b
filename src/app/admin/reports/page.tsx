'use client';

import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, BarChart3, ArrowLeft, Users, BookOpen, Calendar, TrendingUp, Download, FileText } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { reportsService, ReportStats } from '@/lib/services/reports.service';

export default function ReportsManagement() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [stats, setStats] = useState<ReportStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!loading && (!user || !['admin', 'super_admin'].includes(user.profile?.role || ''))) {
      router.push('/');
      return;
    }

    if (user && ['admin', 'super_admin'].includes(user.profile?.role || '')) {
      fetchStats();
    }
  }, [user, loading]);

  const fetchStats = async () => {
    try {
      setIsLoading(true);
      const response = await reportsService.getStats();
      
      if (response.success && response.data) {
        setStats(response.data);
      }
    } catch (err) {
      console.error('Error fetching stats:', err);
    } finally {
      setIsLoading(false);
    }
  };

  if (loading || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <Loader2 className="mx-auto animate-spin" size={48} />
          <p className="mt-4 text-muted-foreground">Carregando relatórios...</p>
        </div>
      </div>
    );
  }

  if (!user || !['admin', 'super_admin'].includes(user.profile?.role || '')) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-4 sm:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header Section */}
        <div className="bg-gradient-to-r from-[#667eea] to-[#764ba2] rounded-2xl shadow-xl p-6 sm:p-8 mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex flex-col sm:flex-row sm:items-center gap-4">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <BarChart3 className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl sm:text-3xl font-bold text-white">
                    Relatórios
                  </h1>
                  <p className="text-white/90 text-sm sm:text-base">
                    Análises e estatísticas do sistema
                  </p>
                </div>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                onClick={() => router.push('/')}
                variant="secondary"
                className="bg-white/20 hover:bg-white/30 text-white border-white/30 backdrop-blur-sm transition-all duration-300"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Voltar
              </Button>
            </div>
          </div>
        </div>

        {/* Statistics Cards */}
        {stats && (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
            <Card className="shadow-xl border-0 hover:shadow-2xl transition-all duration-300">
              <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-xl font-bold text-gray-900">Total de Alunos</CardTitle>
                <div className="p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
                  <Users className="h-5 w-5 text-white" />
                </div>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="text-3xl font-bold text-gray-900">{stats.totalStudents}</div>
                <p className="text-sm text-gray-600 mt-2">
                  Alunos cadastrados no sistema
                </p>
              </CardContent>
            </Card>

            <Card className="shadow-xl border-0 hover:shadow-2xl transition-all duration-300">
              <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-xl font-bold text-gray-900">Total de Cursos</CardTitle>
                <div className="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
                  <BookOpen className="h-5 w-5 text-white" />
                </div>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="text-3xl font-bold text-gray-900">{stats.totalCourses}</div>
                <p className="text-sm text-gray-600 mt-2">
                  Cursos disponíveis
                </p>
              </CardContent>
            </Card>

            <Card className="shadow-xl border-0 hover:shadow-2xl transition-all duration-300">
              <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-xl font-bold text-gray-900">Total de Turmas</CardTitle>
                <div className="p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg">
                  <Calendar className="h-5 w-5 text-white" />
                </div>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="text-3xl font-bold text-gray-900">{stats.totalClasses}</div>
                <p className="text-sm text-gray-600 mt-2">
                  Turmas ativas e inativas
                </p>
              </CardContent>
            </Card>

            <Card className="shadow-xl border-0 hover:shadow-2xl transition-all duration-300">
              <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-xl font-bold text-gray-900">Matrículas Ativas</CardTitle>
                <div className="p-2 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-white" />
                </div>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="text-3xl font-bold text-gray-900">{stats.activeEnrollments}</div>
                <p className="text-sm text-gray-600 mt-2">
                  Alunos matriculados
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Report Actions */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <Card className="shadow-xl border-0 hover:shadow-2xl transition-all duration-300">
            <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg">
              <CardTitle className="text-xl font-bold text-gray-900 flex items-center">
                <div className="p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg mr-3">
                  <Users className="w-5 h-5 text-white" />
                </div>
                Relatório de Alunos
              </CardTitle>
              <CardDescription className="text-gray-600">
                Lista completa de alunos com informações de contato e status
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-6">
              <Button 
                className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-lg hover:shadow-xl transition-all duration-300" 
                disabled
              >
                <Download className="w-4 h-4 mr-2" />
                Gerar Relatório
              </Button>
            </CardContent>
          </Card>

          <Card className="shadow-xl border-0 hover:shadow-2xl transition-all duration-300">
            <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg">
              <CardTitle className="text-xl font-bold text-gray-900 flex items-center">
                <div className="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg mr-3">
                  <BookOpen className="w-5 h-5 text-white" />
                </div>
                Relatório de Cursos
              </CardTitle>
              <CardDescription className="text-gray-600">
                Informações detalhadas sobre cursos e suas turmas
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-6">
              <Button 
                className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg hover:shadow-xl transition-all duration-300" 
                disabled
              >
                <Download className="w-4 h-4 mr-2" />
                Gerar Relatório
              </Button>
            </CardContent>
          </Card>

          <Card className="shadow-xl border-0 hover:shadow-2xl transition-all duration-300">
            <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg">
              <CardTitle className="text-xl font-bold text-gray-900 flex items-center">
                <div className="p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg mr-3">
                  <Calendar className="w-5 h-5 text-white" />
                </div>
                Relatório de Presença
              </CardTitle>
              <CardDescription className="text-gray-600">
                Frequência e presença dos alunos por período
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-6">
              <Button 
                className="w-full bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300" 
                disabled
              >
                <Download className="w-4 h-4 mr-2" />
                Gerar Relatório
              </Button>
            </CardContent>
          </Card>

          <Card className="shadow-xl border-0 hover:shadow-2xl transition-all duration-300">
            <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg">
              <CardTitle className="text-xl font-bold text-gray-900 flex items-center">
                <div className="p-2 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg mr-3">
                  <TrendingUp className="w-5 h-5 text-white" />
                </div>
                Relatório Financeiro
              </CardTitle>
              <CardDescription className="text-gray-600">
                Análise de receitas e matrículas por período
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-6">
              <Button 
                className="w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-lg hover:shadow-xl transition-all duration-300" 
                disabled
              >
                <Download className="w-4 h-4 mr-2" />
                Gerar Relatório
              </Button>
            </CardContent>
          </Card>

          <Card className="shadow-xl border-0 hover:shadow-2xl transition-all duration-300">
            <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg">
              <CardTitle className="text-xl font-bold text-gray-900 flex items-center">
                <div className="p-2 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg mr-3">
                  <FileText className="w-5 h-5 text-white" />
                </div>
                Relatório Personalizado
              </CardTitle>
              <CardDescription className="text-gray-600">
                Crie relatórios customizados com filtros específicos
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-6">
              <Button 
                className="w-full bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white shadow-lg hover:shadow-xl transition-all duration-300" 
                disabled
              >
                <Download className="w-4 h-4 mr-2" />
                Criar Relatório
              </Button>
            </CardContent>
          </Card>

          <Card className="shadow-xl border-0 hover:shadow-2xl transition-all duration-300">
            <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg">
              <CardTitle className="text-xl font-bold text-gray-900 flex items-center">
                <div className="p-2 bg-gradient-to-r from-pink-500 to-pink-600 rounded-lg mr-3">
                  <BarChart3 className="w-5 h-5 text-white" />
                </div>
                Dashboard Executivo
              </CardTitle>
              <CardDescription className="text-gray-600">
                Visão geral com gráficos e métricas principais
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-6">
              <Button 
                className="w-full bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 text-white shadow-lg hover:shadow-xl transition-all duration-300" 
                disabled
              >
                <TrendingUp className="w-4 h-4 mr-2" />
                Ver Dashboard
              </Button>
            </CardContent>
          </Card>
        </div>

        <Card className="mt-8 shadow-xl border-0">
          <CardContent className="pt-6">
            <div className="text-center text-sm text-gray-600">
              <p className="flex items-center justify-center gap-2">
                <span className="text-lg">💡</span>
                <strong className="text-gray-900">Em breve:</strong> 
                Relatórios em PDF, agendamento automático, filtros avançados e muito mais!
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
