import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';
import { prisma } from '@/lib/prisma';
import { sendMagicLinkEmail } from '@/lib/email';
import { sendWhatsAppMagicLink } from '@/lib/whatsapp';
import { isAdminEmail, isSuperAdminEmail } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const { email, phone } = await request.json();

    // Validate input - need either email or phone
    if (!email && !phone) {
      return NextResponse.json({ error: 'Email ou telefone é obrigatório' }, { status: 400 });
    }

    if (email && !email.includes('@')) {
      return NextResponse.json({ error: 'Email válido é obrigatório' }, { status: 400 });
    }

    const emailLower = email?.toLowerCase();

    // Check if user exists in the system
    let user = null;
    let student = null;

    if (email) {
      user = await prisma.user.findUnique({
        where: { email: emailLower },
        include: {
          profile: true
        }
      });
    }

    // If no user found and we have phone, try to find by phone
    if (!user && phone) {
      // First check if there's a user with this phone as email (some systems store phone as email)
      user = await prisma.user.findUnique({
        where: { email: phone },
        include: {
          profile: true
        }
      });
    }

    // Special case: Super admin and admin users can login even if not in database
    if (!user && email && (isSuperAdminEmail(emailLower) || isAdminEmail(emailLower))) {
      const isSuperAdmin = isSuperAdminEmail(emailLower);
      console.log(`🔑 Creating ${isSuperAdmin ? 'super admin' : 'admin'} user on first login: ${emailLower}`);

      // Create admin user on first login
      user = await prisma.user.create({
        data: {
          email: emailLower,
          emailVerified: true,
          profile: {
            create: {
              fullName: isSuperAdmin ? 'Super Admin' : 'Admin',
              role: isSuperAdmin ? 'super_admin' : 'teacher'
            }
          }
        },
        include: {
          profile: true
        }
      });
    }

    // If user doesn't exist, check if they're a student
    if (!user) {
      // Build student search criteria
      const whereClause: any = {
        status: 'active' // Only allow active students to login
      };

      // Search by email or phone
      if (email && phone) {
        whereClause.OR = [
          { email: emailLower },
          { phone: phone }
        ];
      } else if (email) {
        whereClause.email = emailLower;
      } else if (phone) {
        whereClause.phone = phone;
      }

      student = await prisma.student.findFirst({
        where: whereClause
      });

      if (student) {
        console.log(`🎓 Creating user account for student: ${student.email || student.phone}`);
        
        // Create user account for the student
        // Use email if available, otherwise use phone as email
        const userEmail = student.email || phone;
        
        user = await prisma.user.create({
          data: {
            email: userEmail,
            emailVerified: true,
            profile: {
              create: {
                fullName: student.name,
                role: 'student'
              }
            }
          },
          include: {
            profile: true
          }
        });
      } else {
        // Security: Do not send messages to users that don't exist in the system
        // This prevents enumeration attacks and unauthorized access attempts
        console.log(`⚠️  Magic link requested for non-existent user: ${email || phone}`);

        // Return success message to prevent enumeration
        // but don't actually send any message or create any records
        return NextResponse.json({
          message: 'Link de acesso enviado',
        });
      }
    }

    // Update existing user's role if they're in admin emails but don't have admin role
    if (user && user.profile) {
      const isSuperAdmin = isSuperAdminEmail(emailLower);
      const isAdmin = isAdminEmail(emailLower);
      const currentRole = user.profile.role;

      let shouldUpdateRole = false;
      let newRole = currentRole;

      if (isSuperAdmin && currentRole !== 'super_admin') {
        newRole = 'super_admin';
        shouldUpdateRole = true;
      } else if (isAdmin && !['admin', 'super_admin'].includes(currentRole)) {
        newRole = 'admin';
        shouldUpdateRole = true;
      }

      if (shouldUpdateRole) {
        console.log(`🔄 Updating user role from ${currentRole} to ${newRole} for: ${emailLower}`);
        await prisma.userProfile.update({
          where: { userId: user.id },
          data: { role: newRole }
        });
        // Update the user object to reflect the change
        user.profile.role = newRole;
      }
    }

    // Delete any existing magic links for this user
    await prisma.magicLink.deleteMany({
      where: { userId: user.id }
    });

    // Generate magic link token
    const token = crypto.randomBytes(32).toString('hex');
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + parseInt(process.env.MAGIC_LINK_EXPIRY_MINUTES || '15'));

    // Create magic link
    await prisma.magicLink.create({
      data: {
        userId: user.id,
        token,
        email: emailLower || phone, // Use email if available, otherwise phone
        expiresAt
      }
    });

    // Send magic link via email or WhatsApp
    try {
      if (email) {
        await sendMagicLinkEmail(emailLower, token);
        console.log('✅ Magic link email sent successfully to:', email);
      } else if (phone) {
        await sendWhatsAppMagicLink(phone, token);
        console.log('✅ Magic link WhatsApp sent successfully to:', phone);
      }
    } catch (sendError) {
      console.error('❌ Failed to send magic link:', sendError);
      // Don't fail the request if sending fails - user can still use the link if they have it
      // In development, we'll still return the token for testing
    }

    const magicLinkUrl = `${process.env.NEXTAUTH_URL}/auth/verify?token=${token}`;

    return NextResponse.json({
      message: email ? 'Link de acesso enviado para seu email' : 'Link de acesso enviado via WhatsApp',
      // In development, return the token for testing
      ...(process.env.NODE_ENV === 'development' && { token, magicLinkUrl })
    });

  } catch (error) {
    console.error('Error generating magic link:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}
