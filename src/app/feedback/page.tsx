'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { toast } from 'sonner';

interface TokenData {
  tokenId: string;
  lesson: {
    id: string;
    title: string;
    scheduledDate: string;
    class: {
      name: string;
      course: {
        name: string;
      };
    };
  };
  student: {
    id: string;
    name: string;
  };
  existingFeedback?: {
    rating: 'good' | 'neutral' | 'bad';
    anonymousText?: string;
    submittedAt: string;
  };
  expiresAt: string;
}

export default function FeedbackPage() {
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  
  const [tokenData, setTokenData] = useState<TokenData | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [rating, setRating] = useState<'good' | 'neutral' | 'bad' | ''>('');
  const [anonymousText, setAnonymousText] = useState('');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!token) {
      setError('Token de feedback não fornecido');
      setLoading(false);
      return;
    }

    fetchTokenData();
  }, [token]);

  const fetchTokenData = async () => {
    try {
      const response = await fetch(`/api/nps/token?token=${token}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao validar token');
      }

      setTokenData(data.data);
      
      // Pre-fill form if feedback already exists
      if (data.data.existingFeedback) {
        setRating(data.data.existingFeedback.rating);
        setAnonymousText(data.data.existingFeedback.anonymousText || '');
      }
    } catch (error) {
      console.error('Error fetching token data:', error);
      setError(error instanceof Error ? error.message : 'Erro ao carregar dados');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!rating) {
      toast.error('Por favor, selecione uma avaliação');
      return;
    }

    setSubmitting(true);

    try {
      const response = await fetch('/api/nps/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token,
          rating,
          anonymousText: anonymousText.trim() || undefined
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao enviar feedback');
      }

      toast.success('Feedback enviado com sucesso! Obrigado pela sua avaliação.');
      
      // Refresh token data to show updated feedback
      await fetchTokenData();
    } catch (error) {
      console.error('Error submitting feedback:', error);
      toast.error(error instanceof Error ? error.message : 'Erro ao enviar feedback');
    } finally {
      setSubmitting(false);
    }
  };

  const getRatingEmoji = (ratingValue: string) => {
    switch (ratingValue) {
      case 'good': return '😊';
      case 'neutral': return '😐';
      case 'bad': return '😞';
      default: return '';
    }
  };

  const getRatingLabel = (ratingValue: string) => {
    switch (ratingValue) {
      case 'good': return 'Boa';
      case 'neutral': return 'Neutra';
      case 'bad': return 'Ruim';
      default: return '';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Carregando...</p>
        </div>
      </div>
    );
  }

  if (error || !tokenData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h1 className="text-xl font-bold text-gray-900 mb-2">Erro</h1>
          <p className="text-gray-600 mb-4">{error || 'Token inválido ou expirado'}</p>
          <p className="text-sm text-gray-500">
            Se você recebeu este link por WhatsApp, verifique se o link está correto e não expirou.
          </p>
        </div>
      </div>
    );
  }

  const lessonDate = new Date(tokenData.lesson.scheduledDate).toLocaleDateString('pt-BR');
  const isExpired = new Date(tokenData.expiresAt) < new Date();

  if (isExpired) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center">
          <div className="text-orange-500 text-6xl mb-4">⏰</div>
          <h1 className="text-xl font-bold text-gray-900 mb-2">Link Expirado</h1>
          <p className="text-gray-600">
            Este link de feedback expirou. Links de feedback são válidos por 24 horas após o envio.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          {/* Header */}
          <div className="bg-blue-600 text-white p-6">
            <h1 className="text-2xl font-bold mb-2">📝 Avaliação da Aula</h1>
            <p className="text-blue-100">Como foi sua experiência na aula?</p>
          </div>

          {/* Lesson Info */}
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">Informações da Aula</h2>
            <div className="space-y-2 text-gray-600">
              <p><strong>Curso:</strong> {tokenData.lesson.class.course.name}</p>
              <p><strong>Turma:</strong> {tokenData.lesson.class.name}</p>
              <p><strong>Aula:</strong> {tokenData.lesson.title}</p>
              <p><strong>Data:</strong> {lessonDate}</p>
              <p><strong>Aluno:</strong> {tokenData.student.name}</p>
            </div>
          </div>

          {/* Feedback Form */}
          <form onSubmit={handleSubmit} className="p-6">
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Como você avalia esta aula? *
              </label>
              <div className="grid grid-cols-3 gap-4">
                {(['good', 'neutral', 'bad'] as const).map((ratingValue) => (
                  <button
                    key={ratingValue}
                    type="button"
                    onClick={() => setRating(ratingValue)}
                    className={`p-4 rounded-lg border-2 transition-all ${
                      rating === ratingValue
                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                        : 'border-gray-200 hover:border-gray-300 text-gray-600'
                    }`}
                  >
                    <div className="text-3xl mb-2">{getRatingEmoji(ratingValue)}</div>
                    <div className="font-medium">{getRatingLabel(ratingValue)}</div>
                  </button>
                ))}
              </div>
            </div>

            <div className="mb-6">
              <label htmlFor="anonymousText" className="block text-sm font-medium text-gray-700 mb-2">
                Comentário adicional (opcional e anônimo)
              </label>
              <textarea
                id="anonymousText"
                value={anonymousText}
                onChange={(e) => setAnonymousText(e.target.value)}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Deixe aqui seus comentários sobre a aula (opcional)..."
                maxLength={500}
              />
              <p className="text-xs text-gray-500 mt-1">
                Máximo 500 caracteres. Este comentário é anônimo.
              </p>
            </div>

            {tokenData.existingFeedback && (
              <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md">
                <p className="text-sm text-green-700">
                  ✅ Você já enviou feedback para esta aula em{' '}
                  {new Date(tokenData.existingFeedback.submittedAt).toLocaleString('pt-BR')}.
                  Você pode alterar sua avaliação a qualquer momento.
                </p>
              </div>
            )}

            <button
              type="submit"
              disabled={!rating || submitting}
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {submitting ? 'Enviando...' : tokenData.existingFeedback ? 'Atualizar Feedback' : 'Enviar Feedback'}
            </button>
          </form>
        </div>

        {/* Footer */}
        <div className="text-center mt-6 text-sm text-gray-500">
          <p>VoxStudent - Sistema de Feedback de Aulas</p>
          <p>Este link expira em {new Date(tokenData.expiresAt).toLocaleString('pt-BR')}</p>
        </div>
      </div>
    </div>
  );
}
