import { prisma } from '@/lib/prisma';
import { randomBytes } from 'crypto';
import { addHours } from 'date-fns';

export interface NpsTokenData {
  id: string;
  token: string;
  lessonId: string;
  studentId: string;
  expiresAt: Date;
}

export interface NpsFeedbackData {
  rating: 'good' | 'neutral' | 'bad';
  anonymousText?: string;
}

export interface NpsFeedbackSubmission extends NpsFeedbackData {
  tokenId: string;
  lessonId: string;
  studentId: string;
}

// NPS token validity period in hours (default to 24 if not set in env)
const NPS_TOKEN_VALIDITY_HOURS = Number(process.env.NPS_TOKEN_VALIDITY_HOURS) || 24;

export class NpsService {
  /**
   * Generate a secure NPS feedback token for a student and lesson
   */
  static async generateNpsToken(lessonId: string, studentId: string): Promise<NpsTokenData> {
    try {
      // Check if token already exists for this lesson and student
      const existingToken = await prisma.npsToken.findUnique({
        where: {
          lessonId_studentId: {
            lessonId,
            studentId
          }
        }
      });

      if (existingToken && existingToken.expiresAt > new Date()) {
        // Return existing valid token
        return {
          id: existingToken.id,
          token: existingToken.token,
          lessonId: existingToken.lessonId,
          studentId: existingToken.studentId,
          expiresAt: existingToken.expiresAt
        };
      }

      // Generate new secure token
      const token = randomBytes(32).toString('hex');
      const expiresAt = addHours(new Date(), NPS_TOKEN_VALIDITY_HOURS);

      // Delete existing token if it exists (expired or used)
      if (existingToken) {
        await prisma.npsToken.delete({
          where: { id: existingToken.id }
        });
      }

      // Create new token
      const npsToken = await prisma.npsToken.create({
        data: {
          token,
          lessonId,
          studentId,
          expiresAt
        }
      });

      return {
        id: npsToken.id,
        token: npsToken.token,
        lessonId: npsToken.lessonId,
        studentId: npsToken.studentId,
        expiresAt: npsToken.expiresAt
      };
    } catch (error) {
      console.error('Error generating NPS token:', error);
      throw new Error('Failed to generate NPS feedback token');
    }
  }

  /**
   * Validate and retrieve NPS token data
   */
  static async validateNpsToken(token: string): Promise<NpsTokenData | null> {
    try {
      const npsToken = await prisma.npsToken.findUnique({
        where: { token },
        include: {
          lesson: {
            include: {
              class: {
                include: {
                  course: true
                }
              }
            }
          },
          student: true,
          feedback: true
        }
      });

      if (!npsToken) {
        return null;
      }

      // Check if token is expired
      if (npsToken.expiresAt < new Date()) {
        return null;
      }

      return {
        id: npsToken.id,
        token: npsToken.token,
        lessonId: npsToken.lessonId,
        studentId: npsToken.studentId,
        expiresAt: npsToken.expiresAt
      };
    } catch (error) {
      console.error('Error validating NPS token:', error);
      return null;
    }
  }

  /**
   * Submit NPS feedback
   */
  static async submitFeedback(data: NpsFeedbackSubmission): Promise<boolean> {
    try {
      // Validate token first
      const tokenData = await this.validateNpsToken(data.tokenId);
      if (!tokenData) {
        throw new Error('Invalid or expired NPS token');
      }

      // Check if feedback already exists
      const existingFeedback = await prisma.npsFeedback.findUnique({
        where: { tokenId: data.tokenId }
      });

      if (existingFeedback) {
        // Update existing feedback
        await prisma.npsFeedback.update({
          where: { tokenId: data.tokenId },
          data: {
            rating: data.rating,
            anonymousText: data.anonymousText || null,
            updatedAt: new Date()
          }
        });
      } else {
        // Create new feedback
        await prisma.npsFeedback.create({
          data: {
            tokenId: data.tokenId,
            lessonId: data.lessonId,
            studentId: data.studentId,
            rating: data.rating,
            anonymousText: data.anonymousText || null
          }
        });
      }

      // Mark token as used
      await prisma.npsToken.update({
        where: { id: tokenData.id },
        data: { usedAt: new Date() }
      });

      return true;
    } catch (error) {
      console.error('Error submitting NPS feedback:', error);
      throw error;
    }
  }

  /**
   * Get NPS feedback for a lesson
   */
  static async getLessonFeedback(lessonId: string) {
    try {
      return await prisma.npsFeedback.findMany({
        where: { lessonId },
        include: {
          student: {
            select: {
              id: true,
              name: true
            }
          },
          lesson: {
            include: {
              class: {
                include: {
                  course: true
                }
              }
            }
          }
        },
        orderBy: { submittedAt: 'desc' }
      });
    } catch (error) {
      console.error('Error getting lesson feedback:', error);
      throw error;
    }
  }

  /**
   * Get NPS statistics for a course
   */
  static async getCourseFeedbackStats(courseId: string) {
    try {
      const feedback = await prisma.npsFeedback.findMany({
        where: {
          lesson: {
            class: {
              courseId
            }
          }
        },
        select: {
          rating: true,
          submittedAt: true
        }
      });

      const total = feedback.length;
      const good = feedback.filter(f => f.rating === 'good').length;
      const neutral = feedback.filter(f => f.rating === 'neutral').length;
      const bad = feedback.filter(f => f.rating === 'bad').length;

      return {
        total,
        good,
        neutral,
        bad,
        goodPercentage: total > 0 ? Math.round((good / total) * 100) : 0,
        neutralPercentage: total > 0 ? Math.round((neutral / total) * 100) : 0,
        badPercentage: total > 0 ? Math.round((bad / total) * 100) : 0
      };
    } catch (error) {
      console.error('Error getting course feedback stats:', error);
      throw error;
    }
  }

  /**
   * Clean up expired NPS tokens
   */
  static async cleanupExpiredTokens(): Promise<number> {
    try {
      const result = await prisma.npsToken.deleteMany({
        where: {
          expiresAt: {
            lt: new Date()
          }
        }
      });

      console.log(`🧹 Cleaned up ${result.count} expired NPS tokens`);
      return result.count;
    } catch (error) {
      console.error('Error cleaning up expired NPS tokens:', error);
      throw error;
    }
  }

  /**
   * Get detailed token information for feedback form
   */
  static async getTokenDetails(token: string) {
    try {
      const npsToken = await prisma.npsToken.findUnique({
        where: { token },
        include: {
          lesson: {
            include: {
              class: {
                include: {
                  course: true
                }
              }
            }
          },
          student: {
            select: {
              id: true,
              name: true
            }
          },
          feedback: true
        }
      });

      if (!npsToken || npsToken.expiresAt < new Date()) {
        return null;
      }

      return npsToken;
    } catch (error) {
      console.error('Error getting token details:', error);
      return null;
    }
  }
}
